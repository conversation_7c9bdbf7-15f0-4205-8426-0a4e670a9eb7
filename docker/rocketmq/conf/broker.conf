# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Broker 基本配置
brokerClusterName=DefaultCluster
brokerName=broker-a
brokerId=0

# NameServer地址 - 修复连接问题
namesrvAddr=localhost:9876

# Broker对外服务的监听端口
listenPort=10911

# 删除文件时间点，默认凌晨4点
deleteWhen=04

# 文件保留时间，默认72小时
fileReservedTime=72

# Broker的角色
# - ASYNC_MASTER 异步复制Master
# - SYNC_MASTER 同步双写Master
# - SLAVE
brokerRole=ASYNC_MASTER

# 刷盘方式
# - ASYNC_FLUSH 异步刷盘
# - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH

# 自动创建Topic
autoCreateTopicEnable=true

# 自动创建订阅组
autoCreateSubscriptionGroup=true

# 消息存储路径
storePathRootDir=/opt/store
storePathCommitLog=/opt/store/commitlog

# 检测物理文件磁盘空间
diskMaxUsedSpaceRatio=95

# 限制的消息大小
maxMessageSize=65536

# Broker IP地址，配置为127.0.0.1以便宿主机应用访问
brokerIP1=127.0.0.1

# 发消息线程池数量
sendMessageThreadPoolNums=128

# 拉消息线程池数量
pullMessageThreadPoolNums=128

# 强制指定本机IP，避免Docker网络问题
# 对于宿主机应用访问，使用localhost
useVIPChannel=false
